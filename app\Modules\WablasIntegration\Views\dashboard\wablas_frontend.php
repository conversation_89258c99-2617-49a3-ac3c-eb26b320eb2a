<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Wablas Integration Dashboard' ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        :root {
            --wablas-primary: #25D366;
            --wablas-secondary: #128C7E;
            --wablas-accent: #075E54;
            --wablas-light: #DCF8C6;
            --wablas-dark: #34495e;
            --wablas-danger: #e74c3c;
            --wablas-warning: #f39c12;
            --wablas-info: #3498db;
            --wablas-success: #27ae60;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--wablas-primary) !important;
        }

        .main-container {
            padding: 2rem 0;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--wablas-primary), var(--wablas-secondary));
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card.danger {
            background: linear-gradient(135deg, var(--wablas-danger), #c0392b);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, var(--wablas-warning), #e67e22);
        }

        .stat-card.info {
            background: linear-gradient(135deg, var(--wablas-info), #2980b9);
        }

        .stat-card.success {
            background: linear-gradient(135deg, var(--wablas-success), #229954);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 3rem;
            opacity: 0.3;
        }

        .device-status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-connected {
            background: var(--wablas-light);
            color: var(--wablas-accent);
        }

        .status-disconnected {
            background: #ffebee;
            color: #c62828;
        }

        .status-error {
            background: #fff3e0;
            color: #ef6c00;
        }

        .progress-custom {
            height: 8px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
        }

        .progress-bar-custom {
            border-radius: 10px;
            background: linear-gradient(90deg, var(--wablas-primary), var(--wablas-secondary));
        }

        .btn-wablas {
            background: linear-gradient(135deg, var(--wablas-primary), var(--wablas-secondary));
            border: none;
            color: white;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-wablas:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
            color: white;
        }

        .table-custom {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .table-custom thead {
            background: linear-gradient(135deg, var(--wablas-primary), var(--wablas-secondary));
            color: white;
        }

        .table-custom tbody tr:hover {
            background: rgba(37, 211, 102, 0.05);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--wablas-danger);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .sidebar-item {
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .sidebar-item:hover {
            background: rgba(37, 211, 102, 0.1);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, var(--wablas-primary), var(--wablas-secondary));
            color: white;
        }

        .quick-action {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 15px;
            padding: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .quick-action:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--wablas-primary), var(--wablas-secondary));
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6);
        }

        .message-bubble {
            background: var(--wablas-light);
            border-radius: 15px 15px 15px 5px;
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            position: relative;
        }

        .message-bubble.sent {
            background: var(--wablas-primary);
            color: white;
            border-radius: 15px 15px 5px 15px;
            margin-left: auto;
            max-width: 70%;
        }

        .device-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .device-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .device-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .device-indicator.connected {
            background: var(--wablas-success);
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
        }

        .device-indicator.disconnected {
            background: var(--wablas-danger);
        }

        .device-indicator.connecting {
            background: var(--wablas-warning);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= base_url('wablas') ?>">
                <i class="fab fa-whatsapp me-2"></i>
                WablasFrontEnd
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="<?= base_url('wablas') ?>">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('wablas/devices') ?>">
                            <i class="fas fa-mobile-alt me-1"></i>Devices
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('wablas/messages') ?>">
                            <i class="fas fa-comments me-1"></i>Messages
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('wablas/contacts') ?>">
                            <i class="fas fa-address-book me-1"></i>Contacts
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('wablas/settings') ?>">Settings</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('wablas/install') ?>">Installation</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('logout') ?>">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid main-container" style="margin-top: 80px;">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <h6 class="text-muted mb-3">QUICK ACTIONS</h6>
                    <div class="sidebar-item active" onclick="showDashboard()">
                        <i class="fas fa-chart-line me-2"></i>Overview
                    </div>
                    <div class="sidebar-item" onclick="showMessages()">
                        <i class="fas fa-paper-plane me-2"></i>Send Message
                    </div>
                    <div class="sidebar-item" onclick="showDevices()">
                        <i class="fas fa-mobile-alt me-2"></i>Device Status
                    </div>
                    <div class="sidebar-item" onclick="showContacts()">
                        <i class="fas fa-users me-2"></i>Manage Contacts
                    </div>
                    <div class="sidebar-item" onclick="showReports()">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </div>
                    <div class="sidebar-item" onclick="showSettings()">
                        <i class="fas fa-cog me-2"></i>Settings
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="dashboard-card">
                    <div class="card-body">
                        <h6 class="card-title text-muted mb-3">SYSTEM STATUS</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="device-indicator connected"></div>
                            <small>API Connected</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="device-indicator connected"></div>
                            <small><?= $stats['devices']['connected'] ?? 0 ?> Devices Online</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="device-indicator <?= ($stats['messages']['today']['total'] ?? 0) > 0 ? 'connected' : 'disconnected' ?>"></div>
                            <small><?= $stats['messages']['today']['total'] ?? 0 ?> Messages Today</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-10 col-md-9">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0 text-white">
                            <i class="fab fa-whatsapp me-2"></i>
                            Wablas Integration Dashboard
                        </h1>
                        <p class="text-white-50 mb-0">Manage your WhatsApp messaging with ease</p>
                    </div>
                    <div>
                        <button class="btn btn-wablas me-2" onclick="sendQuickMessage()">
                            <i class="fas fa-paper-plane me-1"></i>Quick Send
                        </button>
                        <button class="btn btn-outline-light" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-number"><?= $stats['devices']['total'] ?? 0 ?></div>
                            <div class="stat-label">Total Devices</div>
                            <div class="stat-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card success">
                            <div class="stat-number"><?= $stats['messages']['today']['sent'] ?? 0 ?></div>
                            <div class="stat-label">Messages Sent</div>
                            <div class="stat-icon">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card info">
                            <div class="stat-number"><?= $stats['contacts']['total'] ?? 0 ?></div>
                            <div class="stat-label">Total Contacts</div>
                            <div class="stat-icon">
                                <i class="fas fa-address-book"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card warning">
                            <div class="stat-number"><?= $stats['schedules']['pending'] ?? 0 ?></div>
                            <div class="stat-label">Pending Messages</div>
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Dashboard Content -->
                <div class="row">
                    <!-- Message Chart -->
                    <div class="col-lg-8 mb-4">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-line text-primary me-2"></i>
                                        Message Activity
                                    </h5>
                                    <select class="form-select form-select-sm" style="width: auto;" id="chartPeriod">
                                        <option value="week">Last 7 Days</option>
                                        <option value="month">Last 30 Days</option>
                                        <option value="year">Last Year</option>
                                    </select>
                                </div>
                                <div class="chart-container">
                                    <canvas id="messageChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Device Status -->
                    <div class="col-lg-4 mb-4">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-mobile-alt text-success me-2"></i>
                                    Device Status
                                </h5>
                                <div id="deviceStatusContainer">
                                    <?php if (!empty($device_status)): ?>
                                        <?php foreach ($device_status as $device): ?>
                                            <div class="device-card">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <h6 class="mb-1">
                                                            <div class="device-indicator <?= $device['connection_status'] === 'connected' ? 'connected' : 'disconnected' ?>"></div>
                                                            <?= esc($device['device_name']) ?>
                                                        </h6>
                                                        <small class="text-muted"><?= esc($device['phone_number']) ?></small>
                                                    </div>
                                                    <span class="device-status status-<?= $device['connection_status'] ?>">
                                                        <?= ucfirst($device['connection_status']) ?>
                                                    </span>
                                                </div>
                                                <div class="progress progress-custom mb-2">
                                                    <div class="progress-bar progress-bar-custom"
                                                         style="width: <?= $device['quota_percentage'] ?>%"></div>
                                                </div>
                                                <small class="text-muted">
                                                    Quota: <?= $device['quota_used'] ?>/<?= $device['quota_limit'] ?> (<?= $device['quota_percentage'] ?>%)
                                                </small>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-mobile-alt fa-3x mb-3 opacity-50"></i>
                                            <p>No devices configured</p>
                                            <button class="btn btn-wablas btn-sm" onclick="addDevice()">
                                                <i class="fas fa-plus me-1"></i>Add Device
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Messages and Quick Actions -->
                <div class="row">
                    <!-- Recent Messages -->
                    <div class="col-lg-6 mb-4">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-comments text-info me-2"></i>
                                        Recent Messages
                                    </h5>
                                    <a href="<?= base_url('wablas/messages/history') ?>" class="btn btn-outline-primary btn-sm">
                                        View All
                                    </a>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-custom">
                                        <thead>
                                            <tr>
                                                <th>Contact</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                                <th>Time</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($recent_messages)): ?>
                                                <?php foreach ($recent_messages as $message): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                                    <i class="fas fa-user text-muted"></i>
                                                                </div>
                                                                <div>
                                                                    <div class="fw-bold"><?= esc($message['contact_name'] ?? 'Unknown') ?></div>
                                                                    <small class="text-muted"><?= esc($message['phone_number']) ?></small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary">
                                                                <?= ucfirst($message['message_type']) ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-<?= $message['status'] === 'sent' ? 'success' : ($message['status'] === 'failed' ? 'danger' : 'warning') ?>">
                                                                <?= ucfirst($message['status']) ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">
                                                                <?= date('H:i', strtotime($message['created_at'])) ?>
                                                            </small>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center text-muted py-4">
                                                        <i class="fas fa-inbox fa-2x mb-2 opacity-50"></i>
                                                        <p class="mb-0">No recent messages</p>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="col-lg-6 mb-4">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-bolt text-warning me-2"></i>
                                    Quick Actions
                                </h5>
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="quick-action" onclick="sendSingleMessage()">
                                            <i class="fas fa-paper-plane fa-2x text-primary mb-2"></i>
                                            <h6>Send Message</h6>
                                            <small class="text-muted">Send to single contact</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="quick-action" onclick="sendBulkMessage()">
                                            <i class="fas fa-broadcast-tower fa-2x text-success mb-2"></i>
                                            <h6>Bulk Send</h6>
                                            <small class="text-muted">Send to multiple contacts</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="quick-action" onclick="scheduleMessage()">
                                            <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                            <h6>Schedule</h6>
                                            <small class="text-muted">Schedule for later</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="quick-action" onclick="manageContacts()">
                                            <i class="fas fa-users fa-2x text-warning mb-2"></i>
                                            <h6>Contacts</h6>
                                            <small class="text-muted">Manage contact list</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="floating-action" onclick="showQuickSendModal()" title="Quick Send Message">
        <i class="fas fa-paper-plane"></i>
    </button>

    <!-- Quick Send Modal -->
    <div class="modal fade" id="quickSendModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-paper-plane me-2"></i>Quick Send Message
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="quickSendForm">
                        <div class="mb-3">
                            <label for="quickPhone" class="form-label">Phone Number</label>
                            <input type="text" class="form-control" id="quickPhone" placeholder="+1234567890" required>
                        </div>
                        <div class="mb-3">
                            <label for="quickMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="quickMessage" rows="4" placeholder="Type your message here..." required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="quickDevice" class="form-label">Device</label>
                            <select class="form-select" id="quickDevice">
                                <option value="">Select Device</option>
                                <?php if (!empty($device_status)): ?>
                                    <?php foreach ($device_status as $device): ?>
                                        <option value="<?= $device['id'] ?>"><?= esc($device['device_name']) ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-wablas" onclick="sendQuickMessageNow()">
                        <span class="loading-spinner d-none me-2"></span>
                        Send Message
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        let messageChart;
        let autoRefreshInterval;

        $(document).ready(function() {
            initializeChart();
            startAutoRefresh();

            // Chart period change
            $('#chartPeriod').change(function() {
                updateMessageChart();
            });
        });

        function initializeChart() {
            const ctx = document.getElementById('messageChart').getContext('2d');
            messageChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Sent',
                        data: [12, 19, 3, 5, 2, 3, 9],
                        borderColor: '#25D366',
                        backgroundColor: 'rgba(37, 211, 102, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Delivered',
                        data: [10, 17, 2, 4, 2, 2, 8],
                        borderColor: '#128C7E',
                        backgroundColor: 'rgba(18, 140, 126, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Failed',
                        data: [2, 2, 1, 1, 0, 1, 1],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        function updateMessageChart() {
            const period = $('#chartPeriod').val();

            $.get('<?= base_url('wablas/dashboard/message-chart-data') ?>', {
                period: period
            }).done(function(response) {
                if (response.success) {
                    messageChart.data.labels = response.data.labels;
                    messageChart.data.datasets[0].data = response.data.datasets.sent;
                    messageChart.data.datasets[1].data = response.data.datasets.delivered;
                    messageChart.data.datasets[2].data = response.data.datasets.failed;
                    messageChart.update();
                }
            }).fail(function() {
                console.log('Failed to update chart data');
            });
        }

        function refreshDashboard() {
            // Add loading state
            const refreshBtn = event.target;
            const originalHtml = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
            refreshBtn.disabled = true;

            // Refresh statistics
            $.get('<?= base_url('wablas/dashboard/stats') ?>').done(function(response) {
                if (response.success) {
                    updateStatistics(response.data);
                }
            });

            // Refresh device status
            $.get('<?= base_url('wablas/dashboard/device-status') ?>').done(function(response) {
                if (response.success) {
                    updateDeviceStatus(response.data);
                }
            });

            // Reset button after 2 seconds
            setTimeout(() => {
                refreshBtn.innerHTML = originalHtml;
                refreshBtn.disabled = false;
            }, 2000);
        }

        function updateStatistics(stats) {
            $('.stat-card:nth-child(1) .stat-number').text(stats.devices.total);
            $('.stat-card:nth-child(2) .stat-number').text(stats.messages.today.sent);
            $('.stat-card:nth-child(3) .stat-number').text(stats.contacts.total);
            $('.stat-card:nth-child(4) .stat-number').text(stats.schedules.pending);
        }

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                updateMessageChart();
                // Refresh other components silently
            }, 30000); // Every 30 seconds
        }

        function showQuickSendModal() {
            $('#quickSendModal').modal('show');
        }

        function sendQuickMessageNow() {
            const phone = $('#quickPhone').val();
            const message = $('#quickMessage').val();
            const device = $('#quickDevice').val();

            if (!phone || !message) {
                alert('Please fill in all required fields');
                return;
            }

            const btn = event.target;
            const spinner = btn.querySelector('.loading-spinner');
            const originalText = btn.innerHTML;

            // Show loading state
            spinner.classList.remove('d-none');
            btn.innerHTML = '<span class="loading-spinner me-2"></span>Sending...';
            btn.disabled = true;

            $.post('<?= base_url('wablas/messages/send-quick') ?>', {
                phone: phone,
                message: message,
                device_id: device
            }).done(function(response) {
                if (response.success) {
                    $('#quickSendModal').modal('hide');
                    $('#quickSendForm')[0].reset();
                    showNotification('Message sent successfully!', 'success');
                } else {
                    showNotification('Failed to send message: ' + response.error, 'error');
                }
            }).fail(function() {
                showNotification('Network error occurred', 'error');
            }).always(function() {
                // Reset button
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        function showNotification(message, type) {
            // Create notification element
            const notification = $(`
                <div class="alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed"
                     style="top: 100px; right: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);

            $('body').append(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.alert('close');
            }, 5000);
        }

        // Sidebar navigation functions
        function showDashboard() {
            $('.sidebar-item').removeClass('active');
            event.target.classList.add('active');
            // Load dashboard content
        }

        function showMessages() {
            window.location.href = '<?= base_url('wablas/messages') ?>';
        }

        function showDevices() {
            window.location.href = '<?= base_url('wablas/devices') ?>';
        }

        function showContacts() {
            window.location.href = '<?= base_url('wablas/contacts') ?>';
        }

        function showReports() {
            window.location.href = '<?= base_url('wablas/reports') ?>';
        }

        function showSettings() {
            window.location.href = '<?= base_url('wablas/settings') ?>';
        }

        // Quick action functions
        function sendSingleMessage() {
            showQuickSendModal();
        }

        function sendBulkMessage() {
            window.location.href = '<?= base_url('wablas/messages/bulk') ?>';
        }

        function scheduleMessage() {
            window.location.href = '<?= base_url('wablas/messages/schedule') ?>';
        }

        function manageContacts() {
            window.location.href = '<?= base_url('wablas/contacts') ?>';
        }

        function addDevice() {
            window.location.href = '<?= base_url('wablas/devices/create') ?>';
        }

        function sendQuickMessage() {
            showQuickSendModal();
        }
    </script>
</body>
</html>
